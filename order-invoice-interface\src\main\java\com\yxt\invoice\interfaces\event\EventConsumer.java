package com.yxt.invoice.interfaces.event;

import static com.yxt.invoice.interfaces.config.ThreadPoolConfig.LONG_ASYNC_POOL;

import com.yxt.invoice.domain.event.create.InvoiceCheckProviderPullEvent;
import com.yxt.invoice.infrastructure.event.EventHandler;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class EventConsumer {


  @Resource
  private EventHandler eventHandler;


  @Async(LONG_ASYNC_POOL)
  @EventListener(InvoiceCheckProviderPullEvent.class)
  public void onEvent(InvoiceCheckProviderPullEvent event) {
    eventHandler.handle(event);
  }



}
