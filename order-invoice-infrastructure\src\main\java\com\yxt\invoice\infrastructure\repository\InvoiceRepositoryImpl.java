package com.yxt.invoice.infrastructure.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.yxt.invoice.domain.command.ExistsOrderInvoiceCommand;
import com.yxt.invoice.domain.command.QueryInvoiceListCommand;
import com.yxt.invoice.domain.model.InvoiceDetail;
import com.yxt.invoice.domain.model.InvoiceMain;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.repository.InvoiceRepository;
import com.yxt.invoice.domain.utils.OrderDateUtils;
import com.yxt.invoice.infrastructure.converter.InvoiceDOConverter;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.doc.EsInvoiceMain;
import com.yxt.invoice.infrastructure.db.es.es_invoice_main.mapper.EsInvoiceMainMapper;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceDetailDO;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceMainDO;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceDetailMapper;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceMainMapper;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryData;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosInvoiceQueryReq;
import com.yxt.invoice.infrastructure.provider.dto.pos.PosResponse;
import com.yxt.invoice.infrastructure.provider.feign.HdPosFeign;
import com.yxt.lang.constants.ApiBizCodeEnum;
import com.yxt.lang.dto.api.PageDTO;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.easyes.core.biz.EsPageInfo;
import org.dromara.easyes.core.conditions.select.LambdaEsQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 发票仓储实现
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Repository
@Slf4j
public class InvoiceRepositoryImpl implements InvoiceRepository {


  @Autowired
  private InvoiceMainMapper invoiceMainMapper;


  @Autowired
  private InvoiceDetailMapper invoiceDetailMapper;


  @Resource
  private HdPosFeign hdPosFeign;


  @Resource
  private EsInvoiceMainMapper esInvoiceMainMapper;


  @Override
  public List<InvoiceMain> findInvoiceManByOrderNo(String orderNo) {
    // 使用LambdaQueryWrapper查询明细
    LambdaQueryWrapper<InvoiceMainDO> detailWrapper = new LambdaQueryWrapper<>();
    detailWrapper.eq(InvoiceMainDO::getOrderNo, orderNo).eq(InvoiceMainDO::getIsValid, 1)
        .eq(InvoiceMainDO::getInvoiceRedBlueType, InvoiceRedBlueTypeEnum.TAX_INVOICE.getCode())
        .orderByAsc(InvoiceMainDO::getId);

    List<InvoiceMainDO> detailDOList = invoiceMainMapper.selectList(detailWrapper);
    return detailDOList.stream().map(InvoiceDOConverter::toDomain).collect(Collectors.toList());
  }


  @Override
  public PageDTO<InvoiceMain> findInvoiceManByConditions(QueryInvoiceListCommand condition) {
    LambdaEsQueryWrapper<EsInvoiceMain> queryWrapper = new LambdaEsQueryWrapper<>();
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getUserId()), EsInvoiceMain::getUserId,
        condition.getUserId());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getCompanyCode()),
        EsInvoiceMain::getCompanyCode, condition.getCompanyCode());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getOrganizationCode()),
        EsInvoiceMain::getOrganizationCode, condition.getOrganizationCode());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getInvoiceMainNo()),
        EsInvoiceMain::getInvoiceMainNo, condition.getInvoiceMainNo());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getThirdPlatformCode()),
        EsInvoiceMain::getThirdPlatformCode, condition.getThirdPlatformCode());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getThirdOrderNo()),
        EsInvoiceMain::getThirdOrderNo, condition.getThirdOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getOrderNo()), EsInvoiceMain::getOrderNo,
        condition.getOrderNo());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getPosNo()), EsInvoiceMain::getPosNo,
        condition.getPosNo());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getInvoiceRedBlueType()),
        EsInvoiceMain::getInvoiceRedBlueType, condition.getInvoiceRedBlueType());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getInvoiceStatus()),
        EsInvoiceMain::getInvoiceStatus, condition.getInvoiceStatus());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getBuyerPartyType()),
        EsInvoiceMain::getBuyerPartyType, condition.getBuyerPartyType());
    queryWrapper.eq(StringUtils.isNotEmpty(condition.getSyncStatus()), EsInvoiceMain::getSyncStatus,
        condition.getSyncStatus());
    if (condition.getBizCode().equals(ApiBizCodeEnum.WECHAT_APPLET.getCode())
        || condition.getBizCode().equals(ApiBizCodeEnum.ALIPAY_APPLET.getCode())) {
      queryWrapper.eq(EsInvoiceMain::getIsValid, 1);
      queryWrapper.eq(EsInvoiceMain::getInvoiceRedBlueType,
          InvoiceRedBlueTypeEnum.TAX_INVOICE.getCode());
    }
    queryWrapper.ge(Objects.nonNull(condition.getStartDate()), EsInvoiceMain::getApplyTime,
        OrderDateUtils.formatYYMMDD(condition.getStartDate()));
    queryWrapper.le(Objects.nonNull(condition.getEndDate()), EsInvoiceMain::getApplyTime,
        OrderDateUtils.formatYYMMDD(condition.getEndDate()));
    queryWrapper.orderByDesc(EsInvoiceMain::getApplyTime);
    EsPageInfo<EsInvoiceMain> esPage = esInvoiceMainMapper.pageQuery(queryWrapper,
        condition.getCurrentPage().intValue(), condition.getPageSize().intValue());

    List<InvoiceMain> resList = Lists.newArrayList();
    if (esPage.getTotal() != 0L) {
      resList = esPage.getList().stream().map(esInvoiceMain -> {
        LambdaQueryWrapper<InvoiceMainDO> detailWrapper = new LambdaQueryWrapper<>();
        detailWrapper.eq(InvoiceMainDO::getInvoiceMainNo, esInvoiceMain.getInvoiceMainNo());
        InvoiceMainDO invoiceMainDO = invoiceMainMapper.selectOne(detailWrapper);
        return InvoiceDOConverter.toDomain(invoiceMainDO);
      }).collect(Collectors.toList());
    }
    PageDTO<InvoiceMain> pageRes = new PageDTO<>();
    pageRes.setTotalCount(esPage.getTotal());
    pageRes.setTotalPage((long) esPage.getPages());
    pageRes.setData(resList);
    pageRes.setCurrentPage(condition.getCurrentPage());
    pageRes.setPageSize(condition.getPageSize());
    return pageRes;
  }

  @Override
  public Boolean exists(ExistsOrderInvoiceCommand command) {
    LambdaQueryWrapper<InvoiceMainDO> detailWrapper = new LambdaQueryWrapper<>();
    detailWrapper.eq(InvoiceMainDO::getOrderNo, command.getOrderNo())
        .eq(InvoiceMainDO::getIsValid, 1)
        .eq(InvoiceMainDO::getInvoiceRedBlueType, InvoiceRedBlueTypeEnum.TAX_INVOICE.getCode());

    InvoiceMainDO invoiceMainDO = invoiceMainMapper.selectOne(detailWrapper);
    if (Objects.nonNull(invoiceMainDO)) {
      return true;
    }

    PosInvoiceQueryReq req = new PosInvoiceQueryReq();
    req.setThirdOrderNo(invoiceMainDO.getThirdOrderNo());
    req.setStoreCode(invoiceMainDO.getOrganizationCode());

    PosResponse<List<PosInvoiceQueryData>> posResponse = hdPosFeign.invoiceQuery(req);
    Boolean success = posResponse.success();
    if (success) {
      List<PosInvoiceQueryData> dataList = posResponse.getData();
      if (CollectionUtils.isEmpty(dataList)) {
        return Boolean.FALSE;
      }

      // 主要看responseId有值的数据
      List<PosInvoiceQueryData> validDataList = dataList.stream()
          .filter(s -> StringUtils.isNotEmpty(s.getResponseId())).collect(Collectors.toList());
      if (CollectionUtils.isEmpty(validDataList)) {
        return Boolean.FALSE;
      } else {
        return Boolean.TRUE;
      }
    }
    return Boolean.FALSE;
  }

  @Override
  @Transactional
  public InvoiceAggregate doSave(InvoiceAggregate aggregate) {
    InvoiceMain invoice = aggregate.getInvoiceMain();
    List<InvoiceDetail> invoiceDetailList = aggregate.getInvoiceDetailList();
    InvoiceMainDO invoiceMainDO = InvoiceDOConverter.toDO(invoice);
    List<InvoiceDetailDO> invoiceDetailDOList = invoiceDetailList.stream()
        .map(InvoiceDOConverter::toDO).collect(Collectors.toList());
    int insertInvoice = invoiceMainMapper.insert(invoiceMainDO);
    if (insertInvoice <= 0) {
      throw new RuntimeException("插入发票主表失败");
    }

    if (!CollectionUtils.isEmpty(invoiceDetailDOList)) {
      for (InvoiceDetailDO invoiceDetailDO : invoiceDetailDOList) {
        int insertDetail = invoiceDetailMapper.insert(invoiceDetailDO);
        if (insertDetail <= 0) {
          throw new RuntimeException("插入发票明细失败");
        }
      }
    }
    return aggregate;
  }

  @Override
  @Transactional
  public InvoiceAggregate doSaveRedInvoice(InvoiceAggregate invoiceAggregate,
      InvoiceAggregate redInvoiceAggregate) {

    Preconditions.checkArgument(
        !invoiceAggregate.isRedCreditInvoice() && redInvoiceAggregate.isRedCreditInvoice(),
        "冲红遇到非法数据,请检查");
    doSave(redInvoiceAggregate);
    //更新原始发票状态
    InvoiceMainDO originInvoice = new InvoiceMainDO();
    originInvoice.setInvoiceStatus(InvoiceStatusEnum.RED_PROGRESSING.getCode());
    originInvoice.setUpdated(LocalDateTime.now());
    originInvoice.setVersion(invoiceAggregate.getInvoiceMain().getVersion() + 1);
    int insert2 = invoiceMainMapper.update(originInvoice,
        new LambdaQueryWrapper<InvoiceMainDO>().eq(InvoiceMainDO::getInvoiceMainNo,
                invoiceAggregate.getInvoiceMain().getRedInvoiceMainNo())
            .lt(InvoiceMainDO::getVersion, originInvoice.getVersion()));
    if (insert2 <= 0) {
      throw new RuntimeException("更新原始发票失败");
    }
    return redInvoiceAggregate;
  }

  @Override
  public InvoiceAggregate findByOrderNo(String orderNo) {

    // 使用LambdaQueryWrapper查询主表
    LambdaQueryWrapper<InvoiceMainDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InvoiceMainDO::getOrderNo, orderNo)
        .eq(InvoiceMainDO::getInvoiceRedBlueType, InvoiceRedBlueTypeEnum.TAX_INVOICE.getCode())
        .eq(InvoiceMainDO::getIsValid, 1);

    InvoiceMainDO invoiceMainDO = invoiceMainMapper.selectOne(wrapper);
    if (invoiceMainDO == null) {
      return null;
    }

    // 使用LambdaQueryWrapper查询明细
    LambdaQueryWrapper<InvoiceDetailDO> detailWrapper = new LambdaQueryWrapper<>();
    detailWrapper.eq(InvoiceDetailDO::getInvoiceMainNo, invoiceMainDO.getInvoiceMainNo())
        .eq(InvoiceDetailDO::getIsValid, 1).orderByAsc(InvoiceDetailDO::getRowNo);

    List<InvoiceDetailDO> detailDOList = invoiceDetailMapper.selectList(detailWrapper);

    // 转换为Domain对象并重建聚合根
    InvoiceMain invoice = InvoiceDOConverter.toDomain(invoiceMainDO);
    List<InvoiceDetail> details = InvoiceDOConverter.toDetailDomainList(detailDOList);

    return InvoiceAggregate.rebuild(invoice, details);
  }

  @Override
  public InvoiceAggregate findByInvoiceMainNo(String invoiceMainNo) {
    log.info("根据开票单号查询发票，开票单号：{}", invoiceMainNo);

    // 使用LambdaQueryWrapper查询主表
    LambdaQueryWrapper<InvoiceMainDO> wrapper = new LambdaQueryWrapper<>();
    wrapper.eq(InvoiceMainDO::getInvoiceMainNo, invoiceMainNo);

    InvoiceMainDO invoiceMainDO = invoiceMainMapper.selectOne(wrapper);
    if (invoiceMainDO == null) {
      return null;
    }

    // 使用LambdaQueryWrapper查询明细
    LambdaQueryWrapper<InvoiceDetailDO> detailWrapper = new LambdaQueryWrapper<>();
    detailWrapper.eq(InvoiceDetailDO::getInvoiceMainNo, invoiceMainNo)
        .orderByAsc(InvoiceDetailDO::getRowNo);

    List<InvoiceDetailDO> detailDOList = invoiceDetailMapper.selectList(detailWrapper);

    // 转换为Domain对象并重建聚合根
    InvoiceMain invoice = InvoiceDOConverter.toDomain(invoiceMainDO);
    List<InvoiceDetail> details = InvoiceDOConverter.toDetailDomainList(detailDOList);

    return InvoiceAggregate.rebuild(invoice, details);
  }

}
