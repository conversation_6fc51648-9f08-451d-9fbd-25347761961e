package com.yxt.invoice.infrastructure.repository;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.yxt.invoice.domain.model.aggregate.InvoiceAggregate;
import com.yxt.invoice.domain.model.valueobject.ProviderInvoiceInfo;
import com.yxt.invoice.domain.model.valueobject.ProviderParam;
import com.yxt.invoice.domain.repository.ProviderInvoiceRepository;
import com.yxt.invoice.infrastructure.db.mysql.entity.InvoiceMainDO;
import com.yxt.invoice.infrastructure.db.mysql.mapper.InvoiceMainMapper;
import com.yxt.invoice.infrastructure.provider.convert.TaxCloudConvert;
import com.yxt.invoice.infrastructure.provider.dto.req.GetInvoiceByResponseIdReqDto;
import com.yxt.invoice.infrastructure.provider.dto.req.PositiveInvoiceIssueReqDto;
import com.yxt.invoice.infrastructure.provider.dto.req.PostNegativeInvoiceIssueReqDto;
import com.yxt.invoice.infrastructure.provider.dto.res.GetInvoiceByResponseIdResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.PositiveInvoiceIssueResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.PostNegativeInvoiceIssueResDto;
import com.yxt.invoice.infrastructure.provider.dto.res.TaxCloudResponse;
import com.yxt.invoice.infrastructure.provider.feign.TaxCloudFeign;
import com.yxt.order.types.invoice.enums.InvoiceRedBlueTypeEnum;
import com.yxt.order.types.invoice.enums.InvoiceStatusEnum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 发票仓储接口
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Component
public class ProviderInvoiceRepositoryImpl implements ProviderInvoiceRepository {

    @Resource
    private TaxCloudFeign taxCloudFeign;
    @Value("spring.profiles.active:prod")
    private String env;
    @Autowired
    private InvoiceMainMapper invoiceMainMapper;


    @Override
    public boolean applyProviderInvoice(InvoiceAggregate aggregate) {
        PositiveInvoiceIssueReqDto positiveInvoiceIssueReqDto = new PositiveInvoiceIssueReqDto();
        PositiveInvoiceIssueReqDto.InvoiceData invoiceData = TaxCloudConvert.convertToPositiveInvoiceIssueReqDto(aggregate);
        if (!"prod".equals(env)) {
            invoiceData.setSellerNumber("1000");
        }
        positiveInvoiceIssueReqDto.setData(Lists.newArrayList(invoiceData));
        TaxCloudResponse<List<PositiveInvoiceIssueResDto>> response = taxCloudFeign.positiveInvoiceBatch(positiveInvoiceIssueReqDto);
        //返回处理
        if (response.success() && !response.getData().isEmpty()) {
            PositiveInvoiceIssueResDto resDto = response.getData().get(0);
            if (resDto.isSuccess()) {
                List<ProviderParam> providerParamList = Lists.newArrayList();
                providerParamList.add(new ProviderParam("responseId", resDto.getResponseId()));
                String providerParam = JSON.toJSONString(providerParamList);
                //更新原始发票状态
                InvoiceMainDO originInvoice = new InvoiceMainDO();
                originInvoice.setInvoiceCode(resDto.getInvoiceCode());
                originInvoice.setUpdated(LocalDateTime.now());
                originInvoice.setProviderParam(providerParam);
                originInvoice.setVersion(aggregate.getInvoiceMain().getVersion() + 1);
                int insert2 = invoiceMainMapper.update(originInvoice, new LambdaQueryWrapper<InvoiceMainDO>().eq(InvoiceMainDO::getInvoiceMainNo, aggregate.getInvoiceMain().getInvoiceMainNo()).lt(InvoiceMainDO::getVersion, originInvoice.getVersion()));
                if (insert2 <= 0) {
                    throw new RuntimeException("Provider 回写发票号失败");
                }
                aggregate.updateApplyInfo(resDto.getInvoiceCode(),providerParam);

                return true;
            }

        }


        return false;
    }


    @Override
    @Transactional
    public boolean applyProviderRedInvoice(InvoiceAggregate invoiceAggregate, InvoiceAggregate redInvoiceAggregate) {
        PostNegativeInvoiceIssueReqDto postNegativeInvoiceIssueReqDto = new PostNegativeInvoiceIssueReqDto();
        PostNegativeInvoiceIssueReqDto.NegativeInvoiceData negativeInvoiceData = TaxCloudConvert.convertToPostNegativeInvoiceIssueReqDto(redInvoiceAggregate);
        postNegativeInvoiceIssueReqDto.setData(negativeInvoiceData);
        TaxCloudResponse<PostNegativeInvoiceIssueResDto> response = taxCloudFeign.postNegativeInvoiceIssue(postNegativeInvoiceIssueReqDto);
        //返回处理
        if (response.success() && response.getData() != null) {
            PostNegativeInvoiceIssueResDto resDto = response.getData();
            List<ProviderParam> providerParamList = Lists.newArrayList();
            providerParamList.add(new ProviderParam("responseId", resDto.getResponseId()));
            providerParamList.add(new ProviderParam("uuid", resDto.getUuid()));
            providerParamList.add(new ProviderParam("confirmCode", resDto.getConfirmCode()));
            providerParamList.add(new ProviderParam("confirmStatus", resDto.getConfirmStatus()));
            String invoiceStatus;
            if (resDto.getConfirmStatus().equals("01")) {
                invoiceStatus = InvoiceStatusEnum.SUCCESS.getCode();

            } else {
                invoiceStatus = InvoiceStatusEnum.FAIL.getCode();

            }
            //更新红票
            InvoiceMainDO redInvoice = new InvoiceMainDO();
            redInvoice.setInvoiceStatus(invoiceStatus);
            redInvoice.setUpdated(LocalDateTime.now());
            redInvoice.setProviderParam(JSON.toJSONString(providerParamList));
            redInvoice.setVersion(redInvoiceAggregate.getInvoiceMain().getVersion() + 1);
            int insert1 = invoiceMainMapper.update(redInvoice, new LambdaQueryWrapper<InvoiceMainDO>().eq(InvoiceMainDO::getInvoiceMainNo, redInvoiceAggregate.getInvoiceMain().getInvoiceMainNo()).lt(InvoiceMainDO::getVersion, redInvoice.getVersion()));
            if (insert1 <= 0) {
                throw new RuntimeException("Provider 回写发票号失败");
            }
            //更新蓝票
            if (invoiceStatus.equals(InvoiceStatusEnum.SUCCESS.getCode())) {
                InvoiceMainDO originInvoice = new InvoiceMainDO();
                originInvoice.setInvoiceStatus(InvoiceStatusEnum.RED_SUCCESS.getCode());
                originInvoice.setUpdated(LocalDateTime.now());
                originInvoice.setVersion(invoiceAggregate.getInvoiceMain().getVersion() + 1);
                int insert2 = invoiceMainMapper.update(originInvoice, new LambdaQueryWrapper<InvoiceMainDO>().eq(InvoiceMainDO::getInvoiceMainNo, invoiceAggregate.getInvoiceMain().getRedInvoiceMainNo()).lt(InvoiceMainDO::getVersion, originInvoice.getVersion()));
                if (insert2 <= 0) {
                    throw new RuntimeException("Provider 回写发票号失败");
                }
            }
            return true;
        }
        return false;

    }


    @Override
    public ProviderInvoiceInfo getProviderInvoiceInfo(InvoiceAggregate aggregate) {


        GetInvoiceByResponseIdReqDto reqDto = TaxCloudConvert.convertToGetInvoiceByResponseIdReqDto(aggregate);
        TaxCloudResponse<GetInvoiceByResponseIdResDto> response = taxCloudFeign.getInvoiceByOutRequestCode(reqDto);

        //返回处理
        if (response.success() && response.getData() != null) {
            GetInvoiceByResponseIdResDto data = response.getData();
            String invoiceStatus = "";
            if ("01,02,03,04,05,08".contains(data.getInvoiceStatus())) {
                invoiceStatus = InvoiceStatusEnum.PROCESS.getCode();

            } else if ("06".contains(data.getInvoiceStatus())) {
                invoiceStatus = InvoiceStatusEnum.SUCCESS.getCode();

            } else if ("07".contains(data.getInvoiceStatus())) {
                invoiceStatus = InvoiceStatusEnum.RED_SUCCESS.getCode();
            }
            String redInvoiceStatus = null;
            if (data.getInvoiceTag().equals("1")) {
                if ("0,1,3".contains(data.getWriteOffState())) {
                    redInvoiceStatus = InvoiceStatusEnum.RED_PROCESS.getCode();
                } else if ("2".contains(data.getWriteOffState())) {
                    redInvoiceStatus = InvoiceStatusEnum.RED_SUCCESS.getCode();
                }
            }


            ProviderInvoiceInfo invoiceInfo = new ProviderInvoiceInfo();
            invoiceInfo.setResponseId(data.getResponseId());
            invoiceInfo.setInvoiceMainNo(aggregate.getInvoiceMain().getInvoiceMainNo());
            invoiceInfo.setInvoiceNo(data.getInvoiceCode());
            invoiceInfo.setPdfUrl(data.getDownPdf());
            invoiceInfo.setInvoiceRedBlueType(data.getInvoiceTag().equals("0") ? InvoiceRedBlueTypeEnum.TAX_INVOICE : InvoiceRedBlueTypeEnum.CREDIT_NOTE);
            invoiceInfo.setChannel(data.getChannel());
            invoiceInfo.setPId(data.getPId());
            invoiceInfo.setApplyTime(data.getIssueDate());
            invoiceInfo.setUploadStatus(data.getUploadStatus());
            invoiceInfo.setInvoiceStatus(invoiceStatus);
            invoiceInfo.setPriceTaxAmount(data.getPriceTaxAmount());
            invoiceInfo.setInvoiceAmount(data.getInvoiceAmount());
            invoiceInfo.setTaxAmount(data.getTaxAmount());
            invoiceInfo.setRedInvoiceStatus(redInvoiceStatus);
            invoiceInfo.setRedInvoiceAmount(data.getHzcxje());
            invoiceInfo.setRedTaxAmount(data.getHzcxse());
            invoiceInfo.setConfirmStatus(data.getHzqrxxztDm());
            invoiceInfo.setUuid(data.getUuid());
            invoiceInfo.setConfirmCode(data.getHzfpxxqrdbh());
            invoiceInfo.setRedInvoiceReason(data.getChyyDm());
            invoiceInfo.setStatusMsg(data.getStatusMsg());

            return invoiceInfo;
        }

        return null;
    }

}
