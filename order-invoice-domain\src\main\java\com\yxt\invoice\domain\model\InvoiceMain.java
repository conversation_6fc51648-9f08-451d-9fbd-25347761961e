package com.yxt.invoice.domain.model;

import com.yxt.order.types.invoice.enums.*;
import com.yxt.order.types.offline.OfflineOrderNo;
import com.yxt.order.types.offline.OfflineThirdOrderNo;
import lombok.Data;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import org.apache.logging.log4j.util.Strings;

/**
 * 发票聚合根
 * 根据数据库表 invoice_main 设计
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2025-08-06
 */
@Data
public class InvoiceMain {

    /**
     * 发票ID
     */
    private Long id;

    /**
     * 分公司编码
     */
    private String companyCode;

    /**
     * 分公司名称
     */
    private String companyName;

    /**
     * 所属机构编码
     */
    private String organizationCode;

    /**
     * 所属机构名称
     */
    private String organizationName;

    /**
     * 开票单号
     */
    private String invoiceMainNo;

    /**
     * 平台编码
     */
    private String thirdPlatformCode;

    /**
     * 第三方平台订单号
     */
    private OfflineThirdOrderNo thirdOrderNo;

    /**
     * 订单号
     */
    private OfflineOrderNo orderNo;

    /**
     * pos销售单号
     */
    private String posNo;


    /**
     * 会员编号
     */
    private String userId;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 交易场景 online:线上交易, offline:线下交易
     */
    private String transactionChannel;


    /**
     * 业务类型 O2O、B2C、B2B
     */
    private String businessType;

    /**
     * 发票供应方编码
     */
    private String providerCode;

    /**
     * 发票代码（税务云回调写入）
     */
    private String invoiceCode;

    /**
     * 发票号码（税务云回调写入）
     */
    private String invoiceNo;

    /**
     * 蓝票:Tax_Invoice 红票:Credit_Note
     */
    private InvoiceRedBlueTypeEnum invoiceRedBlueType;

    /**
     * 红冲对应原发票号,红票必填
     */
    private String redInvoiceMainNo;

    /**
     * 红冲原因
     * 01: 开票有误
     * 02: 销货退回
     * 03: 服务中止
     * 04: 销售折让
     */
    private String redInvoiceReason;

    /**
     *  备注
     */
    private String notes;

    /**
     * 发票类型
     */
    private InvoiceTypeEnum invoiceType;

    /**
     * 发票状态
     */
    private InvoiceStatusEnum invoiceStatus;

    /**
     * 同步状态
     */
    private InvoiceSyncStatusEnum syncStatus;

    /**
     * 实付金额
     */
    private BigDecimal actualPayAmount;

    /**
     * 配送费
     */
    private BigDecimal deliveryAmount;

    /**
     * 配送方式 PlatformFulfillment:平台配送 MerchantFulfillment:商家自配
     */
    private String deliveryType;

    /**
     * 发票金额
     */
    private BigDecimal invoiceAmount;

    /**
     * 税额
     */
    private BigDecimal taxAmount;

    /**
     * 价税合计
     */
    private BigDecimal priceTaxAmount;

    /**
     * 拆票标记 SPLIT-拆 NOT-不拆
     */
    private String splitBill;

    /**
     * 订单创单时间
     */
    private LocalDateTime orderCreated;

    /**
     * 电子发票PDF地址
     */
    private String pdfUrl;

    /**
     * 失败原因
     */
    private String invoiceErrMsg;

    /**
     * 申请开票时间
     */
    private LocalDateTime applyTime;

    /**
     * 开票完成时间
     */
    private LocalDateTime completeTime;

    /**
     * 开票人
     */
    private String operator;

    /**
     * 收款人
     */
    private String payee;

    /**
     * 复核人
     */
    private String reviewed;

    /**
     * 申请渠道 一心到家-YXDJ 心云-XY 海典H2-H2POS
     */
    private String applyChannel;

    /**
     * 开票主体
     */
    private String sellerNumber;

    /**
     * 开票主体名称
     */
    private String sellerName;

    /**
     * 开票纳税人识别号
     */
    private String sellerTin;

    /**
     * 开票主体地址
     */
    private String sellerAddress;

    /**
     * 开票主体电话
     */
    private String sellerPhone;

    /**
     * 开票主体银行
     */
    private String sellerBank;

    /**
     * 开票主体银行账户
     */
    private String sellerBankAccount;

    /**
     * 购方类型
     */
    private InvoiceBuyerPartyTypeEnum buyerPartyType;

    /**
     * 购方名称
     */
    private String buyerName;

    /**
     * 购方税号（个人身份证/单位纳税人识别号）
     */
    private String buyerTin;

    /**
     * 购方地址
     */
    private String buyerAddress;

    /**
     * 购方电话
     */
    private String buyerPhone;

    /**
     * 购方银行
     */
    private String buyerBank;

    /**
     * 购方银行账户
     */
    private String buyerBankAccount;

    /**
     * 购方邮箱
     */
    private String buyerEmail;

    /**
     * 购方手机号
     */
    private String buyerMobile;

    /**
     * 显示购方银行账户 SHOW-显示 HIDE-不显示
     */
    private String showBuyerBankAccount;

    /**
     * 是否起效 1-起效 -1-未起效
     */
    private Long isValid;
    /**
     * 供应商参数
     * List<ProviderParam></>
     */
    private String providerParam;


    /**
     * 平台创建时间
     */
    private LocalDateTime created;

    /**
     * 平台更新时间
     */
    private LocalDateTime updated;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新人
     */
    private String updatedBy;

    /**
     * 系统创建时间
     */
    private LocalDateTime sysCreateTime;

    /**
     * 系统更新时间
     */
    private LocalDateTime sysUpdateTime;

    /**
     * 数据版本，每次update+1
     */
    private Long version;

    public boolean isMemberOrder() {
        return null!=this.userId&& !this.userId.isEmpty();
    }

    public void redCreditApply(String invoiceMainNo, String operatorUserId, String redCreditReason , String notes, Date applyTime) {
        this.id=null;
        this.redInvoiceMainNo=this.invoiceMainNo;
        this.invoiceCode = null;
        this.invoiceNo = null;
        this.invoiceRedBlueType=InvoiceRedBlueTypeEnum.CREDIT_NOTE;
        this.redInvoiceReason=redCreditReason;
        this.notes=notes;
        this.invoiceStatus=InvoiceStatusEnum.WAIT;
        this.syncStatus=InvoiceSyncStatusEnum.WAIT;
        this.pdfUrl=null;
        this.invoiceErrMsg=null;
        this.applyTime =LocalDateTime.now();
        if (applyTime != null) {
            this.applyTime = LocalDateTime.ofInstant(Instant.ofEpochSecond(applyTime.getTime()), ZoneId.of("UTC"));
        }
        this.operator=operatorUserId;
        this.created=LocalDateTime.now();
        this.updated=LocalDateTime.now();
        this.invoiceMainNo = invoiceMainNo;

    }

    public boolean isRedCreditInvoice() {
        return  InvoiceRedBlueTypeEnum.CREDIT_NOTE.equals(this.invoiceRedBlueType);
    }


    public InvoiceMain clone(){
        InvoiceMain invoiceMain = new InvoiceMain();
        invoiceMain.setId(this.id);
        invoiceMain.setCompanyCode(this.companyCode);
        invoiceMain.setCompanyName(this.companyName);
        invoiceMain.setOrganizationCode(this.organizationCode);
        invoiceMain.setOrganizationName(this.organizationName);
        invoiceMain.setInvoiceMainNo(this.invoiceMainNo);
        invoiceMain.setThirdPlatformCode(this.thirdPlatformCode);
        invoiceMain.setThirdOrderNo(this.thirdOrderNo);
        invoiceMain.setOrderNo(this.orderNo);
        invoiceMain.setPosNo(this.posNo);
        invoiceMain.setUserId(this.userId);
        invoiceMain.setMerCode(this.merCode);
        invoiceMain.setTransactionChannel(this.transactionChannel);
        invoiceMain.setProviderCode(this.providerCode);
        invoiceMain.setInvoiceCode(this.invoiceCode);
        invoiceMain.setInvoiceNo(this.invoiceNo);
        invoiceMain.setInvoiceRedBlueType(this.invoiceRedBlueType);
        invoiceMain.setRedInvoiceMainNo(this.redInvoiceMainNo);
        invoiceMain.setRedInvoiceReason(this.redInvoiceReason);
        invoiceMain.setNotes(this.notes);
        invoiceMain.setInvoiceType(this.invoiceType);
        invoiceMain.setInvoiceStatus(this.invoiceStatus);
        invoiceMain.setSyncStatus(this.syncStatus);
        invoiceMain.setActualPayAmount(this.actualPayAmount);
        invoiceMain.setDeliveryAmount(this.deliveryAmount);
        invoiceMain.setDeliveryType(this.deliveryType);
        invoiceMain.setInvoiceAmount(this.invoiceAmount);
        invoiceMain.setTaxAmount(this.taxAmount);
        invoiceMain.setPriceTaxAmount(this.priceTaxAmount);
        invoiceMain.setSplitBill(this.splitBill);
        invoiceMain.setOrderCreated(this.orderCreated);
        invoiceMain.setPdfUrl(this.pdfUrl);
        invoiceMain.setInvoiceErrMsg(this.invoiceErrMsg);
        invoiceMain.setApplyTime(this.applyTime);
        invoiceMain.setOperator(this.operator);
        invoiceMain.setPayee(this.payee);
        invoiceMain.setReviewed(this.reviewed);
        invoiceMain.setApplyChannel(this.applyChannel);
        invoiceMain.setSellerNumber(this.sellerNumber);
        invoiceMain.setSellerName(this.sellerName);
        invoiceMain.setSellerTin(this.sellerTin);
        invoiceMain.setSellerAddress(this.sellerAddress);
        invoiceMain.setSellerPhone(this.sellerPhone);
        invoiceMain.setSellerBank(this.sellerBank);
        invoiceMain.setSellerBankAccount(this.sellerBankAccount);
        invoiceMain.setBuyerPartyType(this.buyerPartyType);
        invoiceMain.setBuyerName(this.buyerName);
        invoiceMain.setBuyerTin(this.buyerTin);
        invoiceMain.setBuyerAddress(this.buyerAddress);
        invoiceMain.setBuyerPhone(this.buyerPhone);
        invoiceMain.setBuyerBank(this.buyerBank);
        invoiceMain.setBuyerBankAccount(this.buyerBankAccount);
        invoiceMain.setBuyerEmail(this.buyerEmail);
        invoiceMain.setBuyerMobile(this.buyerMobile);
        invoiceMain.setShowBuyerBankAccount(this.showBuyerBankAccount);
        invoiceMain.setIsValid(this.isValid);
//        invoiceMain.setProviderParam(this.providerParam);
        invoiceMain.setCreated(this.created);
        invoiceMain.setUpdated(this.updated);
        invoiceMain.setCreatedBy(this.createdBy);
        invoiceMain.setUpdatedBy(this.updatedBy);
        invoiceMain.setSysCreateTime(this.sysCreateTime);
        invoiceMain.setSysUpdateTime(this.sysUpdateTime);
        invoiceMain.setVersion(this.version);


        return invoiceMain;
    }

    /**
     * 清空开票主体和购方主体信息
     */
    public void cleanSellerAndBuyerInfo(){
        this.sellerNumber = Strings.EMPTY;
        this.sellerName = Strings.EMPTY;
        this.sellerTin = Strings.EMPTY;
        this.sellerAddress = Strings.EMPTY;
        this.sellerPhone = Strings.EMPTY;
        this.sellerBank = Strings.EMPTY;
        this.sellerBankAccount = Strings.EMPTY;
        this.buyerPartyType  = null;
        this.buyerName  = Strings.EMPTY;
        this.buyerTin  = Strings.EMPTY;
        this.buyerAddress  = Strings.EMPTY;
        this.buyerPhone  = Strings.EMPTY;
        this.buyerBank  = Strings.EMPTY;
        this.buyerBankAccount  = Strings.EMPTY;
        this.buyerEmail  = Strings.EMPTY;
        this.buyerMobile  = Strings.EMPTY;
    }

}
