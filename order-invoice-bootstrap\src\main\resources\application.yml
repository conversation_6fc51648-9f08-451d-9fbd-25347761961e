server:
  port: 8080
api:
  version: 1.0
compensator:
  enabled: true
spring:
  kafka:
    bootstrap-servers: 10.4.3.239:9092,10.4.3.240:9092,10.4.3.241:9092
    producer:
      retries: 3
      batch-size: 16384
      buffer-memory: 33554432
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.apache.kafka.common.serialization.StringSerializer
    consumer:
      auto-offset-reset: earliest
      enable-auto-commit: false
      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
    listener:
      ack-mode: manual
      log-container-config: false
#  data:
#    mongodb:
#      uri: **********************************************************
  profiles:
    active: test
    robot-send: true
  application:
    name: order-invoice-service
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true
  cloud:
    nacos:
      discovery:
        server-addr: http://10.4.3.210:8848; # 测试
        namespace: 63b6732e-80fc-49c2-ac83-4b09e119d48c # 测试
        metadata:
          department: NR
        register-enabled: true
  datasource:
    dynamic:
      primary: master
      strict: false
      datasource:
        master:
          url: ****************************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
        slave:
          url: ****************************************************************************************************************************************************  #开发
          username: agent
          password: WrHNOhOGHR8yzMEgKvao
          driver-class-name: com.mysql.cj.jdbc.Driver
          type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 40
      min-idle: 40
      max-active: 100
      max-wait: 10000
      wall:
        multi-statement-allow: true
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1 FROM DUAL
      # 启用监控统计
      filters: stat,wall  # 必须包含 stat 才能开启监控
      aop-patterns: "com.yxt.order.atom.order_world.mapper.*"
      web-stat-filter:
        enabled: true                   # 启动 StatFilter
        url-pattern: /*                 # 过滤所有url
        exclusions: "*.js,*.gif,*.jpg,*.png,*.css,*.ico,/druid/*" # 排除一些不必要的url
      # 配置监控页面
      stat-view-servlet:
        enabled: true          # 启用监控页面
        url-pattern: /druid/*  # 访问路径（默认即为/druid/*）
        login-username: admin  # 登录用户名
        login-password: 123456 # 登录密
        reset-enable: true     # 允许清空监控数据
        allow:                 # 允许访问的 IP（可选，留空则允许所有）
        deny:                  # 禁止访问的 IP（优先于 allow）
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  jackson:
    time-zone: Asia/Shanghai
    date-format: yyyy-MM-dd HH:mm:ss
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 100MB
#  redis:
#    password: yxt_redis123
#    lettuce:
#      pool:
#        max-idle: 200
#        min-idle: 50
#        max-active: 5000
#        max-wait: 1000
#    timeout: 2000
#    cluster:                                                                                # 此处新增
#      nodes: **********:9000,**********:9002,**********:9003,**********:9004,**********:9005     # 此处新增，集群地址（此处为测试环境地址）
#      max-redirects: 3
#  redisson:
#    config: |
#      clusterServersConfig:
#        idleConnectionTimeout: 10000
#        connectTimeout: 10000
#        timeout: 3000
#        retryAttempts: 3
#        retryInterval: 1500
#        failedSlaveReconnectionInterval: 3000
#        failedSlaveCheckInterval: 60000
#        password: yxt_redis123
#        subscriptionsPerConnection: 5
#        clientName: null
#        loadBalancer: !<org.redisson.connection.balancer.RoundRobinLoadBalancer> {}
#        subscriptionConnectionMinimumIdleSize: 1
#        subscriptionConnectionPoolSize: 50
#        slaveConnectionMinimumIdleSize: 24
#        slaveConnectionPoolSize: 64
#        masterConnectionMinimumIdleSize: 24
#        masterConnectionPoolSize: 64
#        readMode: "SLAVE"
#        subscriptionMode: "SLAVE"
#        nodeAddresses:
#          - "redis://**********:9000"
#          - "redis://**********:9002"
#          - "redis://**********:9003"
#          - "redis://**********:9004"
#          - "redis://**********:9005"
#        scanInterval: 1000
#        pingConnectionInterval: 30000
#        keepAlive: false
#        tcpNoDelay: true
#      threads: 16
#      nettyThreads: 32
#      codec: !<org.redisson.codec.Kryo5Codec> {}
#      transportMode: "NIO"


mybatis-plus:
  mapper-locations: classpath:mapper/*.xml
  configuration:
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-value: 0
      logic-not-delete-value: 1
      update-strategy: not_null

management:
  endpoint:
    mappings:
      enabled: true
    httptrace:
      enabled: true
  endpoints:
    web:
      exposure:
        include: ["*"]
  health:
    elasticsearch:
      enabled: false
    mongo:
      enabled: false
  metrics:
    distribution:
      percentiles-histogram[http.server.requests]: true
      maximum-expected-value[http.server.requests]: 10000 #预期最大值
      minimum-expected-value[http.server.requests]: 1 #预期最小值

swagger:
  enable: true

feign:
  hystrix:
    enabled: false
  okhttp:
    enabled: true
  client:
    config:
      default:
        connectTimeout: 60000
        readTimeout: 60000
        loggerLevel: full
      customer-config:
        connectTimeout: 6000
        readTimeout: 6000
        loggerLevel: full
ribbon:
  ConnectTimeout: 60000
  ReadTimeout: 60000
  MaxAutoRetries: 0
  MaxAutoRetriesNextServer: 0
hystrix:
  command:
    default:
      execution:
        isolation:
          thread:
            strategy: SEMAPHORE
            timeoutInMilliseconds: 60000



alarm:
  robot:
    # 是否开启机器人告警，默认开启；非必填
    enable: true
    # 值班人手机号，英文逗号分隔；非必填
    oncallMobile: 17710036783,17302856015

xxl:
  job:
    admin:
      addresses: https://test-xxl-job-new.hxyxt.com/xxl-job-admin/
    executor:
      appname:
      port:
      ip:
      address:
      logretentiondays: 7
    accessToken: sk_token

logging:
  level:
    root: info
    com.yxt.invoice.infrastructure.db.mysql.mapper: debug
    com.yxt.invoice.infrastructure.provider.feign.HdPosFeign: DEBUG

#rocketmq:
#  producer:
#    group: order-atom-group
#  name-server: **********:9876;**********:9876

#mq:
#  topic:
#    producer:
#      migrationHanaData: TP_ORDER_ATOM_MIGRATION-HANA
#      migrationHanaDataReHandle: TP_ORDER_ATOM_MIGRATION-RE-HANDLE-HANA
#      hdOfflineOrder: TP_ORDER_OFFLINE_SYNC-HD
#      offlineOrderSyncKc: TP_ORDER_OFFLINE_SYNC-KC


canal:
  # 发票查询ES
  invoice: support_chronic_diseases

# 检查是否配置了订单数据源

easy-es:
  enable: true
  address: **********:9200,**********:9200,**********:9200
  schema: http
  username: elastic
  password: ${myEncrypt.des(5ea6b5c12c5c770a08613b039664d784)}
  keep-alive-millis: 18000
  global-config:
    process-index-mode: manual
    # 异步处理索引是否阻塞主线程 默认阻塞 数据量过大时调整为非阻塞异步进行 项目启动更快
    # https://www.easy-es.cn/pages/cc15ba/#%E9%85%8D%E7%BD%AE%E5%90%AF%E7%94%A8%E6%A8%A1%E5%BC%8F
    async-process-index-blocking: false
    print-dsl: true
    db-config:
      # 添加前缀,区分环境
      index-prefix: ${spring.profiles.active}_

# 远程调用
remote:
  pos:
    url: https://api.yxtmart.cn
  tax-cloud:
#    url: http://***********:9966/admin/invoice/api # 旧的,不使用了,使用下面的地址
    url: http://***********:9966/admin/invoice/apiReform
    p-secret: Wsn5ZKoe5ihWf+aGQ7AQBSdBQZw==
    p-id: YXT00010